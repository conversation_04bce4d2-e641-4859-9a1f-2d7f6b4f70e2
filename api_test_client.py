#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞API测试客户端
用于测试API接口并解析返回结果
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class VulnAPIClient:
    """绿盟漏洞API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:45678"):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'VulnAPIClient/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def check_server_status(self) -> Tuple[bool, Optional[Dict]]:
        """
        检查服务器状态
        
        Returns:
            (是否成功, 状态数据)
        """
        try:
            print("🔍 正在检查服务器状态...")
            response = self.session.get(f'{self.base_url}/', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 服务器连接成功")
                
                # 解析状态信息
                status = data.get('status', 'Unknown')
                login_time = data.get('login_time')
                login_duration = data.get('login_duration')
                target_host = data.get('target_host')
                
                print(f"📊 服务器状态: {status}")
                print(f"🎯 目标主机: {target_host}")
                print(f"⏰ 登录时间: {login_time}")
                print(f"⏱️  登录时长: {login_duration}")
                
                return True, data
            else:
                print(f"❌ 服务器响应错误: HTTP {response.status_code}")
                return False, None
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到服务器，请确认服务器已启动")
            return False, None
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False, None
        except Exception as e:
            print(f"❌ 检查服务器状态失败: {e}")
            return False, None
    
    def get_principle_vulns(self) -> Tuple[bool, Optional[Dict]]:
        """
        获取原理扫描漏洞
        
        Returns:
            (是否成功, 漏洞数据)
        """
        try:
            print("\n🛡️  正在获取原理扫描漏洞...")
            response = self.session.get(f'{self.base_url}/principle-vulns', timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('success'):
                    print("✅ 成功获取漏洞数据")
                    return True, data
                else:
                    print(f"❌ 获取失败: {data.get('message', 'Unknown error')}")
                    return False, data
                    
            elif response.status_code == 503:
                print("❌ 服务不可用，可能是登录失败")
                return False, None
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                return False, None
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时，可能是数据量较大")
            return False, None
        except Exception as e:
            print(f"❌ 获取漏洞数据失败: {e}")
            return False, None
    
    def parse_vuln_data(self, data: Dict) -> None:
        """
        解析和显示漏洞数据
        
        Args:
            data: API返回的漏洞数据
        """
        if not data or not data.get('success'):
            print("❌ 无有效数据可解析")
            return
        
        vuln_data = data.get('data', {})
        
        # 基本统计信息
        print("\n📊 === 漏洞统计信息 ===")
        print(f"任务总数: {vuln_data.get('task_count', 0)}")
        print(f"漏洞总数: {vuln_data.get('total_count', 0)}")
        print(f"最后更新: {vuln_data.get('last_update', 'N/A')}")
        
        # 任务ID列表
        task_ids = vuln_data.get('task_ids', [])
        print(f"任务ID列表: {task_ids}")
        
        # 详细统计
        statistics = vuln_data.get('statistics', {})
        if statistics:
            print("\n📈 === 详细统计 ===")
            
            # 等级分布
            level_count = statistics.get('level_count', {})
            severity_dist = statistics.get('severity_distribution', {})
            print("漏洞等级分布:")
            for level, count in severity_dist.items():
                print(f"  {level}: {count} 个")
            
            # 目标分布
            target_count = statistics.get('target_count', {})
            print("目标主机分布:")
            for target, count in target_count.items():
                print(f"  {target}: {count} 个漏洞")
        
        # 漏洞详情
        vulns = vuln_data.get('vulns', [])
        if vulns:
            print(f"\n🔍 === 漏洞详情 (显示前5个) ===")
            for i, vuln in enumerate(vulns[:5], 1):
                print(f"\n漏洞 {i}:")
                print(f"  ID: {vuln.get('id', 'N/A')}")
                print(f"  名称: {vuln.get('i18n_name', 'N/A')}")
                print(f"  等级: {vuln.get('vuln_level', 'N/A')}")
                print(f"  目标: {vuln.get('target', 'N/A')}")
                print(f"  威胁等级: {vuln.get('threat_level', 'N/A')}")
                print(f"  严重性评分: {vuln.get('severity_points', 'N/A')}")
                print(f"  发现日期: {vuln.get('date_found', 'N/A')}")
                
                # 漏洞描述
                description = vuln.get('i18n_description', [])
                if description:
                    print(f"  描述: {' '.join(description)}")
                
                # 解决方案
                solution = vuln.get('i18n_solution', [])
                if solution:
                    print(f"  解决方案: {' '.join(solution)}")
                
                # CVE信息
                cve_info = []
                if vuln.get('cnvd'):
                    cve_info.append(f"CNVD: {vuln['cnvd']}")
                if vuln.get('cve_id'):
                    cve_info.append(f"CVE: {vuln['cve_id']}")
                if vuln.get('cnnvd'):
                    cve_info.append(f"CNNVD: {vuln['cnnvd']}")
                
                if cve_info:
                    print(f"  CVE信息: {', '.join(cve_info)}")
        
        # 任务结果分布
        task_results = vuln_data.get('task_results', {})
        if task_results:
            print(f"\n📋 === 任务结果分布 ===")
            for task_id, task_vulns in task_results.items():
                vuln_count = len(task_vulns) if isinstance(task_vulns, list) else 0
                print(f"  任务 {task_id}: {vuln_count} 个漏洞")
    
    def save_data_to_file(self, data: Dict, filename: str = None) -> None:
        """
        保存数据到文件
        
        Args:
            data: 要保存的数据
            filename: 文件名，默认使用时间戳
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vuln_data_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 数据已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def run_full_test(self) -> None:
        """运行完整的API测试"""
        print("🚀 === 绿盟漏洞API测试客户端 ===")
        print(f"🌐 API服务器: {self.base_url}")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 1. 检查服务器状态
        status_ok, status_data = self.check_server_status()
        if not status_ok:
            print("\n❌ 服务器状态检查失败，终止测试")
            return
        
        # 2. 获取漏洞数据
        vuln_ok, vuln_data = self.get_principle_vulns()
        if not vuln_ok:
            print("\n❌ 漏洞数据获取失败")
            return
        
        # 3. 解析和显示数据
        self.parse_vuln_data(vuln_data)
        
        # 4. 保存数据到文件
        if vuln_data:
            self.save_data_to_file(vuln_data)
        
        print("\n✅ === 测试完成 ===")


def main():
    """主函数"""
    # 创建API客户端
    client = VulnAPIClient()
    
    # 运行完整测试
    client.run_full_test()


if __name__ == "__main__":
    main()
