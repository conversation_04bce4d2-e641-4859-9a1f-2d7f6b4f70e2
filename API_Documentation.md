# 绿盟漏洞API服务器文档

## 概述

绿盟漏洞API服务器是一个基于Flask的HTTP API服务，用于获取绿盟扫描系统中的原理漏洞信息。该服务提供自动登录、会话管理和漏洞数据查询功能。

## 服务器信息

- **默认端口**: 45678
- **绿盟服务器**: ************
- **协议**: HTTP
- **跨域支持**: 已启用CORS

## API接口

### 1. 服务器状态接口

获取服务器基本信息和登录状态。

**接口地址**: `GET /`

**功能**: 
- 显示服务器基本信息
- 检查当前登录状态
- 显示登录时长和超时设置
- 列出所有可用接口

#### curl测试命令

```bash
# 基本请求
curl -X GET http://localhost:45678/

# 格式化JSON输出
curl -X GET http://localhost:45678/ | python -m json.tool

# 显示响应头信息
curl -X GET -i http://localhost:45678/

# 设置超时时间
curl -X GET --connect-timeout 10 --max-time 30 http://localhost:45678/
```

#### 响应示例

```json
{
  "message": "绿盟原理漏洞API服务器",
  "version": "1.0.0",
  "status": "已登录",
  "login_time": "2025-07-16T10:30:45.123456",
  "login_duration": "120秒",
  "login_timeout": "600秒",
  "target_host": "************",
  "endpoints": {
    "GET /": "服务器信息和状态",
    "GET /principle-vulns": "获取所有已完成任务的原理扫描漏洞"
  },
  "features": [
    "自动登录和会话管理",
    "登录状态过期自动重新登录",
    "专注原理扫描漏洞分析",
    "详细的漏洞统计信息"
  ]
}
```

### 2. 原理漏洞查询接口

获取所有已完成任务的原理扫描漏洞详细信息。

**接口地址**: `GET /principle-vulns`

**功能**:
- 自动检查和维护登录状态
- 获取所有已完成的扫描任务
- 分析并返回原理扫描漏洞
- 提供详细的统计信息

#### curl测试命令

```bash
# 基本请求
curl -X GET http://localhost:45678/principle-vulns

# 格式化JSON输出
curl -X GET http://localhost:45678/principle-vulns | python -m json.tool

# 保存响应到文件
curl -X GET http://localhost:45678/principle-vulns -o vulns_data.json

# 显示响应头和状态码
curl -X GET -i http://localhost:45678/principle-vulns

# 静默模式，只显示响应体
curl -X GET -s http://localhost:45678/principle-vulns

# 设置用户代理
curl -X GET -H "User-Agent: VulnScanner/1.0" http://localhost:45678/principle-vulns

# 设置超时和重试
curl -X GET --connect-timeout 10 --max-time 60 --retry 3 http://localhost:45678/principle-vulns
```

#### 响应示例

**成功响应** (HTTP 200):
```json
{
  "success": true,
  "message": "成功获取 4 个任务的原理扫描漏洞",
  "data": {
    "task_count": 4,
    "task_ids": [794, 793, 789, 788],
    "total_count": 2,
    "vulns": [
      {
        "id": 548915,
        "vul_id": 62814,
        "plugin_id": 62813,
        "i18n_name": "Ollama 未授权访问漏洞(CNVD-2025-04094)【原理扫描】",
        "vuln_level": "high",
        "threat_level": 2,
        "severity_points": 7.8,
        "target": "*************",
        "host_count": 1,
        "vuln_count": 1,
        "date_found": "2025-02-14",
        "date_recorded": "2025-02-14",
        "scan_method": 3,
        "familiar": 600,
        "is_dangerous": false,
        "vul_confirmed": false,
        "i18n_description": [
          "Ollama 是一个支持本地和云端运行的 AI 大模型推理工具。",
          "Ollama 默认未设置权限访问，导致未授权的攻击者可以直接使用该模型进行对话。"
        ],
        "i18n_solution": [
          "解决方案：",
          "1.配置身份验证",
          "2.限制IP访问控制"
        ],
        "cnvd": "CNVD-2025-04094",
        "cve_id": "",
        "cnnvd": "",
        "cncve": "",
        "bugtraq_id": "",
        "osvdb": "",
        "edb": "",
        "nsfocus_id": "",
        "ms_security_bulletin": "",
        "cvss": "",
        "i18n_patch": "",
        "i18n_morelinks": "",
        "patch": null,
        "exp_desc": null,
        "reserved1": ["", "", ""],
        "reserved2": null
      }
    ],
    "statistics": {
      "total_count": 2,
      "level_count": {
        "high": 2
      },
      "severity_distribution": {
        "high": 2,
        "medium": 0,
        "low": 0
      },
      "target_count": {
        "*************": 2
      }
    },
    "task_results": {
      "788": [
        {
          "id": 548915,
          "vul_id": 62814,
          "i18n_name": "Ollama 未授权访问漏洞(CNVD-2025-04094)【原理扫描】",
          "vuln_level": "high",
          "target": "*************"
        }
      ],
      "789": [],
      "793": [],
      "794": []
    },
    "last_update": "2025-07-16T11:37:33.624145"
  }
}
```

**服务不可用响应** (HTTP 503):
```json
{
  "success": false,
  "message": "系统登录失败，请检查网络连接或稍后重试",
  "data": []
}
```

**服务器错误响应** (HTTP 500):
```json
{
  "success": false,
  "message": "获取原理扫描漏洞异常: 具体错误信息",
  "data": []
}
```

#### 响应字段详细说明

**根级字段：**
- `success` (boolean): 请求是否成功
- `message` (string): 响应消息，包含操作结果描述
- `data` (object): 漏洞数据对象

**data对象字段：**
- `task_count` (integer): 扫描任务总数
- `task_ids` (array): 任务ID列表，数字类型
- `total_count` (integer): 原理扫描漏洞总数
- `vulns` (array): 漏洞详情列表
- `statistics` (object): 统计信息对象
- `task_results` (object): 按任务分组的漏洞结果
- `last_update` (string): 最后更新时间，ISO格式

**漏洞对象字段 (vulns数组中的每个对象)：**
- `id` (integer): 漏洞记录ID
- `vul_id` (integer): 漏洞ID
- `plugin_id` (integer): 插件ID
- `i18n_name` (string): 漏洞名称（中文）
- `vuln_level` (string): 漏洞等级 (high/medium/low)
- `threat_level` (integer): 威胁等级数值
- `severity_points` (float): 严重性评分
- `target` (string): 目标主机IP
- `host_count` (integer): 受影响主机数量
- `vuln_count` (integer): 漏洞数量
- `date_found` (string): 发现日期 (YYYY-MM-DD)
- `date_recorded` (string): 记录日期 (YYYY-MM-DD)
- `scan_method` (integer): 扫描方法代码
- `familiar` (integer): 熟悉度评分
- `is_dangerous` (boolean): 是否危险
- `vul_confirmed` (boolean): 是否已确认
- `i18n_description` (array): 漏洞描述数组
- `i18n_solution` (array): 解决方案数组
- `cnvd` (string): CNVD编号
- `cve_id` (string): CVE编号
- `cnnvd` (string): CNNVD编号
- `cncve` (string): CNCVE编号
- `bugtraq_id` (string): Bugtraq ID
- `osvdb` (string): OSVDB编号
- `edb` (string): Exploit-DB编号
- `nsfocus_id` (string): 绿盟ID
- `ms_security_bulletin` (string): 微软安全公告
- `cvss` (string): CVSS评分
- `i18n_patch` (string): 补丁信息
- `i18n_morelinks` (string): 更多链接
- `patch` (null): 补丁详情
- `exp_desc` (null): 漏洞利用描述
- `reserved1` (array): 保留字段1
- `reserved2` (null): 保留字段2

**统计信息对象字段 (statistics)：**
- `total_count` (integer): 漏洞总数
- `level_count` (object): 按等级统计
- `severity_distribution` (object): 严重性分布
- `target_count` (object): 按目标主机统计

**任务结果对象 (task_results)：**
- 键为任务ID (string)
- 值为该任务的漏洞数组 (array)

## 高级curl用法

### 1. 监控服务状态

```bash
# 每30秒检查一次服务状态
watch -n 30 'curl -s http://localhost:45678/ | python -m json.tool | grep -E "(status|login_duration)"'

# 循环检查服务可用性
while true; do
  if curl -s --connect-timeout 5 http://localhost:45678/ > /dev/null; then
    echo "$(date): 服务正常"
  else
    echo "$(date): 服务不可用"
  fi
  sleep 60
done
```

### 2. 数据处理和分析

```bash
# 获取漏洞总数和任务信息
curl -s http://localhost:45678/principle-vulns | python -c "
import json, sys
data = json.load(sys.stdin)
if data['success']:
    print(f'漏洞总数: {data[\"data\"][\"total_count\"]}')
    print(f'任务总数: {data[\"data\"][\"task_count\"]}')
    print(f'任务ID列表: {data[\"data\"][\"task_ids\"]}')
else:
    print(f'获取失败: {data[\"message\"]}')
"

# 提取高危漏洞
curl -s http://localhost:45678/principle-vulns | python -c "
import json, sys
data = json.load(sys.stdin)
if data['success']:
    high_risk = [v for v in data['data']['vulns'] if v.get('vuln_level') == 'high']
    print(f'高危漏洞数量: {len(high_risk)}')
    for vuln in high_risk[:3]:  # 显示前3个
        print(f'- {vuln[\"i18n_name\"]} (ID: {vuln[\"id\"]})')
        print(f'  目标: {vuln[\"target\"]} | 评分: {vuln[\"severity_points\"]}')
"

# 提取统计信息
curl -s http://localhost:45678/principle-vulns | python -c "
import json, sys
data = json.load(sys.stdin)
if data['success']:
    stats = data['data']['statistics']
    print('漏洞等级分布:')
    for level, count in stats['severity_distribution'].items():
        print(f'  {level}: {count} 个')
    print('目标主机分布:')
    for target, count in stats['target_count'].items():
        print(f'  {target}: {count} 个漏洞')
"

# 提取任务结果分布
curl -s http://localhost:45678/principle-vulns | python -c "
import json, sys
data = json.load(sys.stdin)
if data['success']:
    task_results = data['data']['task_results']
    print('任务漏洞分布:')
    for task_id, vulns in task_results.items():
        count = len(vulns) if isinstance(vulns, list) else 0
        print(f'  任务 {task_id}: {count} 个漏洞')
"
```

### 3. 错误处理和调试

```bash
# 详细调试信息
curl -X GET -v http://localhost:45678/principle-vulns

# 检查HTTP状态码
curl -X GET -w "HTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" -o /dev/null -s http://localhost:45678/principle-vulns

# 测试连接性
curl -X GET --connect-timeout 5 --max-time 10 http://localhost:45678/ || echo "连接失败"
```

## 服务特性

### 自动登录和会话管理
- 服务启动时自动登录绿盟系统
- 登录会话超时时间：600秒（10分钟）
- 会话过期时自动重新登录
- 支持网络异常恢复

### 错误处理
- 网络连接失败：返回503状态码
- 登录失败：自动重试登录
- 数据获取异常：返回500状态码并包含错误详情

### 性能优化
- 支持CORS跨域请求
- JSON格式响应
- 详细的日志记录

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   curl: (7) Failed to connect to localhost port 45678: Connection refused
   ```
   - 检查服务是否启动：`python vuln_api_server.py`
   - 确认端口45678未被占用

2. **服务返回503错误**
   - 检查绿盟服务器连接性
   - 确认登录凭据正确
   - 查看服务器日志

3. **响应时间过长**
   - 增加curl超时时间：`--max-time 120`
   - 检查网络连接质量
   - 查看服务器负载

### 日志查看

服务器运行时会输出详细日志，包括：
- 登录状态变化
- API请求处理
- 错误信息和异常

## 安全注意事项

1. **网络安全**：服务监听在0.0.0.0，确保防火墙配置正确
2. **访问控制**：建议在生产环境中添加认证机制
3. **数据敏感性**：漏洞信息可能包含敏感数据，注意访问权限控制

## 集成示例

### Python集成
```python
import requests
import json

# 获取服务状态
response = requests.get('http://localhost:45678/')
status = response.json()
print(f"服务状态: {status['status']}")

# 获取漏洞数据
response = requests.get('http://localhost:45678/principle-vulns')
if response.status_code == 200:
    data = response.json()
    if data['success']:
        print(f"获取到 {data['data']['total_count']} 个漏洞")
    else:
        print(f"获取失败: {data['message']}")
```

### Shell脚本集成
```bash
#!/bin/bash
API_URL="http://localhost:45678"

# 检查服务状态
check_service() {
    if curl -s --connect-timeout 5 "$API_URL/" > /dev/null; then
        echo "服务正常运行"
        return 0
    else
        echo "服务不可用"
        return 1
    fi
}

# 获取漏洞数据
get_vulns() {
    curl -s "$API_URL/principle-vulns" | python -m json.tool
}

# 主逻辑
if check_service; then
    get_vulns
fi
```

## 专用测试客户端

项目提供了一个专门的测试客户端 `api_test_client.py`，用于完整测试API功能：

### 使用方法

```bash
# 运行测试客户端
python api_test_client.py
```

### 功能特性

- ✅ **自动服务器状态检查** - 验证API服务器是否正常运行
- ✅ **完整数据获取** - 获取所有原理扫描漏洞数据
- ✅ **智能数据解析** - 自动解析和格式化显示漏洞信息
- ✅ **统计信息展示** - 显示漏洞等级分布、目标分布等统计
- ✅ **数据文件保存** - 自动保存JSON格式的原始数据
- ✅ **错误处理** - 完善的异常处理和错误提示
- ✅ **详细日志** - 提供详细的操作日志和进度提示

### 输出示例

```
🚀 === 绿盟漏洞API测试客户端 ===
🌐 API服务器: http://localhost:45678
⏰ 测试时间: 2025-07-16 11:45:30

🔍 正在检查服务器状态...
✅ 服务器连接成功
📊 服务器状态: 已登录
🎯 目标主机: ************
⏰ 登录时间: 2025-07-16T11:37:20.123456
⏱️  登录时长: 480秒

🛡️  正在获取原理扫描漏洞...
✅ 成功获取漏洞数据

📊 === 漏洞统计信息 ===
任务总数: 4
漏洞总数: 2
最后更新: 2025-07-16T11:37:33.624145
任务ID列表: [794, 793, 789, 788]

📈 === 详细统计 ===
漏洞等级分布:
  high: 2 个
  medium: 0 个
  low: 0 个
目标主机分布:
  *************: 2 个漏洞

🔍 === 漏洞详情 (显示前5个) ===

漏洞 1:
  ID: 548915
  名称: Ollama 未授权访问漏洞(CNVD-2025-04094)【原理扫描】
  等级: high
  目标: *************
  威胁等级: 2
  严重性评分: 7.8
  发现日期: 2025-02-14
  描述: Ollama 是一个支持本地和云端运行的 AI 大模型推理工具。Ollama 默认未设置权限访问，导致未授权的攻击者可以直接使用该模型进行对话。
  解决方案: 解决方案： 1.配置身份验证 2.限制IP访问控制
  CVE信息: CNVD: CNVD-2025-04094

📋 === 任务结果分布 ===
  任务 788: 2 个漏洞
  任务 789: 0 个漏洞
  任务 793: 0 个漏洞
  任务 794: 0 个漏洞

💾 数据已保存到: vuln_data_20250716_114530.json

✅ === 测试完成 ===
```

### 自定义配置

```python
# 使用自定义API地址
client = VulnAPIClient(base_url="http://your-server:8080")

# 只检查服务器状态
status_ok, status_data = client.check_server_status()

# 只获取漏洞数据
vuln_ok, vuln_data = client.get_principle_vulns()

# 解析已有的漏洞数据
client.parse_vuln_data(your_data)
```
