#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞API服务器
提供HTTP API接口获取原理漏洞的详细信息
"""

from flask import Flask, jsonify
from flask_cors import CORS
import logging
from datetime import datetime

from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

class VulnAPIServer:
    """漏洞API服务器类"""

    def __init__(self, host="************", server_port=5000):
        """
        初始化API服务器
        参数:
            host: 绿盟服务器地址
            server_port: API服务器端口
        """
        self.host = host
        self.server_port = server_port
        self.app = Flask(__name__)
        CORS(self.app)  # 启用跨域支持

        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 初始化管理器
        self.login_manager = None
        self.task_manager = None
        self.vuln_manager = None
        self.session = None
        self.base_url = None

        # 登录状态
        self.is_logged_in = False
        self.last_login_time = None
        self.login_timeout = 600  # 登录超时时间（秒），默认10 min

        # 注册路由
        self._register_routes()

        # 启动时自动登录
        self._auto_login()
    
    def _register_routes(self):
        """注册API路由"""

        @self.app.route('/', methods=['GET'])
        def index():
            """API首页"""
            # 检查当前登录状态
            login_status = self._check_login()

            # 计算登录时长
            login_duration = None
            if self.last_login_time:
                duration_seconds = (datetime.now() - self.last_login_time).total_seconds()
                login_duration = f"{duration_seconds:.0f}秒"

            return jsonify({
                'message': '绿盟原理漏洞API服务器',
                'version': '1.0.0',
                'status': '已登录' if login_status else '未登录',
                'login_time': self.last_login_time.isoformat() if self.last_login_time else None,
                'login_duration': login_duration,
                'login_timeout': f"{self.login_timeout}秒",
                'target_host': self.host,
                'endpoints': {
                    'GET /': '服务器信息和状态',
                    'GET /principle-vulns': '获取所有已完成任务的原理扫描漏洞'
                },
                'features': [
                    '自动登录和会话管理',
                    '登录状态过期自动重新登录',
                    '专注原理扫描漏洞分析',
                    '详细的漏洞统计信息'
                ]
            })
        
        @self.app.route('/principle-vulns', methods=['GET'])
        def get_all_principle_vulns():
            """获取所有已完成任务的原理扫描漏洞"""
            # 检查登录状态，自动重新登录
            if not self._check_login():
                return jsonify({
                    'success': False,
                    'message': '系统登录失败，请检查网络连接或稍后重试',
                    'data': []
                }), 503

            try:
                # 获取已完成任务
                success, completed_tasks = self.task_manager.get_completed_tasks()
                if not success:
                    # 如果获取任务失败，可能是会话过期，尝试重新登录
                    self.logger.warning("获取任务失败，尝试重新登录...")
                    if self._check_login():
                        success, completed_tasks = self.task_manager.get_completed_tasks()

                if not success or not completed_tasks:
                    return jsonify({
                        'success': False,
                        'message': '没有找到已完成的任务',
                        'data': []
                    })

                # 提取任务ID列表
                task_ids = [task['task_id'] for task in completed_tasks]

                # 分析多个任务的漏洞
                analysis_result = self.vuln_manager.analyze_multiple_tasks_vulns(task_ids)

                return jsonify({
                    'success': True,
                    'message': f'成功获取 {len(task_ids)} 个任务的原理扫描漏洞',
                    'data': {
                        'task_count': len(task_ids),
                        'task_ids': task_ids,
                        'vulns': analysis_result['all_vulns'],
                        'total_count': len(analysis_result['all_vulns']),
                        'statistics': analysis_result['statistics'],
                        'task_results': analysis_result['task_results'],
                        'last_update': datetime.now().isoformat()
                    }
                })

            except Exception as e:
                self.logger.error(f"获取原理扫描漏洞异常: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': f'获取原理扫描漏洞异常: {str(e)}',
                    'data': []
                }), 500
        


    def _check_login(self):
        """检查登录状态，如果过期则自动重新登录"""
        # 检查基本登录状态
        if not self.is_logged_in or self.session is None:
            self.logger.warning("未登录，尝试自动登录...")
            return self._auto_login()

        # 检查登录是否超时
        if self.last_login_time:
            current_time = datetime.now()
            time_diff = (current_time - self.last_login_time).total_seconds()

            if time_diff > self.login_timeout:
                self.logger.warning(f"登录已超时 ({time_diff:.0f}秒)，尝试重新登录...")
                return self._auto_login()

        # 测试会话是否仍然有效
        if not self._test_session():
            self.logger.warning("会话已失效，尝试重新登录...")
            return self._auto_login()

        return True

    def _test_session(self):
        """测试当前会话是否有效"""
        try:
            if not self.task_manager:
                return False

            # 尝试获取任务列表来测试会话
            success, _ = self.task_manager.get_task_list(page=1, page_size=1)
            return success

        except Exception as e:
            self.logger.error(f"测试会话失败: {str(e)}")
            return False

    def _auto_login(self):
        """自动登录绿盟系统，返回登录是否成功"""
        try:
            self.logger.info("正在自动登录绿盟系统...")

            # 重置登录状态
            self.is_logged_in = False
            self.last_login_time = None

            # 初始化登录管理器
            self.login_manager = GreenLeagueLogin(host=self.host)

            # 执行自动登录
            success, _ = self.login_manager.auto_login()

            if success:
                # 初始化其他管理器
                self.session = self.login_manager.get_session()
                self.base_url = self.login_manager.get_base_url()
                self.task_manager = TaskManager(self.session, self.base_url)
                self.vuln_manager = VulnManager(self.session, self.base_url)

                self.is_logged_in = True
                self.last_login_time = datetime.now()

                self.logger.info("✓ 自动登录绿盟系统成功")
                return True
            else:
                self.logger.error("✗ 自动登录绿盟系统失败")
                return False

        except Exception as e:
            self.logger.error(f"自动登录异常: {str(e)}")
            return False

    def run(self, debug=False):
        """启动API服务器"""
        self.logger.info(f"启动原理漏洞API服务器，端口: {self.server_port}")
        self.logger.info(f"绿盟服务器地址: {self.host}")
        self.logger.info("API接口:")
        self.logger.info(f"  GET  http://localhost:{self.server_port}/")
        self.logger.info(f"  GET  http://localhost:{self.server_port}/principle-vulns")

        self.app.run(host='0.0.0.0', port=self.server_port, debug=debug)

def main():
    """主函数 - 启动参数已写死"""
    # 写死的启动参数
    host = '************'  # 绿盟服务器地址
    port = 45678          # API服务器端口
    debug = False         # 调试模式

    # 创建并启动服务器
    server = VulnAPIServer(host=host, server_port=port)
    server.run(debug=debug)

if __name__ == '__main__':
    main()
