#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟漏洞管理类
负责查询任务漏洞分布和过滤原理扫描漏洞
"""

import requests
import json
from collections import defaultdict

class VulnManager:
    """绿盟漏洞管理类"""
    
    def __init__(self, session, base_url):
        """
        初始化漏洞管理器
        参数:
            session: 已认证的requests.Session对象
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url
    
    def get_vuln_distribution(self, task_id, page=1, size=100):
        """
        获取指定任务的漏洞分布信息
        参数:
            task_id: 任务ID
            page: 页码，默认为1
            size: 每页大小，默认为100
        返回: (success, vuln_data)
        """
        try:
            # 构造URL，包含查询参数
            url = f"{self.base_url}/interface/report/sys/vuln-distribution/{task_id}"
            params = {
                'task_ids': task_id,
                'source': 'online',
                'page': page,
                'size': size,
                'filterVulnLevels': 'high,middle,low',
                'vul_category_id': ''
            }
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    vuln_info = data.get('data', {}).get('vulns_info', {})
                    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])
                    print(f"✓ 成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞")
                    return True, data
                else:
                    print(f"✗ 获取任务 {task_id} 漏洞信息失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 请求任务 {task_id} 漏洞信息失败，状态码: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"✗ 获取任务 {task_id} 漏洞信息异常: {str(e)}")
            return False, None
    
    def get_all_vulns_for_task(self, task_id):
        """
        获取指定任务的所有漏洞（自动分页）
        参数:
            task_id: 任务ID
        返回: (success, all_vulns_list)
        """
        all_vulns = []
        page = 1
        size = 100
        
        print(f"正在获取任务 {task_id} 的所有漏洞...")
        
        while True:
            success, vuln_data = self.get_vuln_distribution(task_id, page=page, size=size)
            if not success:
                return False, []
            
            vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
            vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])
            
            if not vuln_list:
                break
            
            all_vulns.extend(vuln_list)

            # 检查是否还有更多页
            # 从响应中获取总数，如果没有更多数据则退出
            total_from_response = vuln_data.get('data', {}).get('total', 0)
            if len(vuln_list) < size or len(all_vulns) >= total_from_response:
                break
            
            page += 1
        
        print(f"✓ 成功获取任务 {task_id} 的所有漏洞，共 {len(all_vulns)} 个")
        return True, all_vulns
    
    def filter_principle_scan_vulns(self, vuln_list):
        """
        过滤出原理扫描的漏洞
        参数:
            vuln_list: 漏洞列表
        返回: 原理扫描漏洞列表
        """
        principle_vulns = []
        
        for vuln in vuln_list:
            vuln_name = vuln.get('i18n_name', '')
            # 检查漏洞名称是否包含"原理扫描"标识
            if '原理扫描' in vuln_name or '【原理扫描】' in vuln_name:
                principle_vulns.append(vuln)
        
        return principle_vulns
    
    def get_principle_scan_vulns_for_task(self, task_id):
        """
        获取指定任务的原理扫描漏洞
        参数:
            task_id: 任务ID
        返回: (success, principle_vulns)
        """
        success, all_vulns = self.get_all_vulns_for_task(task_id)
        if not success:
            return False, []
        
        principle_vulns = self.filter_principle_scan_vulns(all_vulns)
        print(f"✓ 任务 {task_id} 中发现 {len(principle_vulns)} 个原理扫描漏洞")
        
        return True, principle_vulns
    
    def display_vuln_details(self, vulns, title="漏洞详情"):
        """
        显示漏洞详细信息
        参数:
            vulns: 漏洞列表
            title: 显示标题
        """
        if not vulns:
            print(f"没有{title}可显示")
            return
        
        print(f"\n{title} (共 {len(vulns)} 个漏洞):")
        print("=" * 80)
        
        for i, vuln in enumerate(vulns, 1):
            vuln_name = vuln.get('i18n_name', '未知漏洞')
            vuln_level = vuln.get('vuln_level', '未知')
            target = vuln.get('target', '未知目标')
            severity_points = vuln.get('severity_points', 0)
            vuln_id = vuln.get('vuln_id', '未知ID')
            
            print(f"{i:3d}. {vuln_name}")
            print(f"     漏洞ID: {vuln_id}")
            print(f"     风险等级: {vuln_level}")
            print(f"     评分: {severity_points}")
            print(f"     目标: {target}")
            print("-" * 40)
    
    def get_vuln_statistics(self, vulns):
        """
        获取漏洞统计信息
        参数:
            vulns: 漏洞列表
        返回: dict 包含统计信息
        """
        if not vulns:
            return {}
        
        statistics = {
            'total_count': len(vulns),
            'level_count': defaultdict(int),
            'target_count': defaultdict(int),
            'severity_distribution': {
                'high': 0,      # >= 7.0
                'medium': 0,    # 4.0 - 6.9
                'low': 0        # < 4.0
            }
        }
        
        for vuln in vulns:
            # 统计风险等级
            level = vuln.get('vuln_level', '未知')
            statistics['level_count'][level] += 1
            
            # 统计目标
            target = vuln.get('target', '未知目标')
            statistics['target_count'][target] += 1
            
            # 统计严重程度分布
            severity = vuln.get('severity_points', 0)
            try:
                severity_float = float(severity)
                if severity_float >= 7.0:
                    statistics['severity_distribution']['high'] += 1
                elif severity_float >= 4.0:
                    statistics['severity_distribution']['medium'] += 1
                else:
                    statistics['severity_distribution']['low'] += 1
            except (ValueError, TypeError):
                statistics['severity_distribution']['low'] += 1
        
        return statistics
    
    def display_vuln_statistics(self, vulns, title="漏洞统计"):
        """
        显示漏洞统计信息
        参数:
            vulns: 漏洞列表
            title: 显示标题
        """
        statistics = self.get_vuln_statistics(vulns)
        
        if not statistics:
            print(f"没有{title}可显示")
            return
        
        print(f"\n{title}:")
        print("=" * 50)
        print(f"总漏洞数: {statistics['total_count']}")
        
        print("\n按风险等级统计:")
        for level, count in statistics['level_count'].items():
            print(f"  {level}: {count} 个")
        
        print("\n按严重程度分布:")
        severity_dist = statistics['severity_distribution']
        print(f"  高危 (≥7.0): {severity_dist['high']} 个")
        print(f"  中危 (4.0-6.9): {severity_dist['medium']} 个")
        print(f"  低危 (<4.0): {severity_dist['low']} 个")
        
        print("\n按目标统计 (前10个):")
        target_items = sorted(statistics['target_count'].items(), 
                            key=lambda x: x[1], reverse=True)[:10]
        for target, count in target_items:
            print(f"  {target}: {count} 个")
    
    def analyze_multiple_tasks_vulns(self, task_ids, focus_on_principle=True):
        """
        分析多个任务的漏洞信息
        参数:
            task_ids: 任务ID列表
            focus_on_principle: 是否只关注原理扫描漏洞
        返回: dict 包含分析结果
        """
        print(f"\n开始分析 {len(task_ids)} 个任务的漏洞信息...")
        print("=" * 60)
        
        all_vulns = []
        task_results = {}
        
        for task_id in task_ids:
            print(f"\n正在分析任务 {task_id}...")
            
            if focus_on_principle:
                success, vulns = self.get_principle_scan_vulns_for_task(task_id)
            else:
                success, vulns = self.get_all_vulns_for_task(task_id)
            
            if success:
                task_results[task_id] = vulns
                all_vulns.extend(vulns)
                print(f"  任务 {task_id}: 发现 {len(vulns)} 个漏洞")
            else:
                task_results[task_id] = []
                print(f"  任务 {task_id}: 获取漏洞信息失败")
        
        # 汇总统计
        print(f"\n汇总统计:")
        print("-" * 40)
        print(f"总计发现漏洞: {len(all_vulns)} 个")
        
        if all_vulns:
            self.display_vuln_statistics(all_vulns, "汇总漏洞统计")
        
        return {
            'all_vulns': all_vulns,
            'task_results': task_results,
            'statistics': self.get_vuln_statistics(all_vulns)
        }
