# 绿盟漏洞扫描查询系统

一个专业的绿盟漏洞扫描系统集成工具，提供自动登录、任务管理、漏洞分析和HTTP API服务。支持批量查询已完成任务的原理扫描漏洞，并提供详细的统计分析功能。

## ✨ 功能特性

- � **自动登录**: 支持验证码自动识别，一键登录绿盟系统
- 🎯 **专注原理漏洞**: 智能过滤和分析原理扫描漏洞
- 🌐 **HTTP API服务**: 提供RESTful API接口，支持远程调用
- 📊 **智能统计**: 漏洞等级分布、类型统计、任务分析
- 🔄 **会话管理**: 自动维护登录状态，会话过期自动重新登录
- 🌍 **跨域支持**: 内置CORS支持，可与前端应用无缝集成
- 📋 **标准格式**: 所有数据采用JSON格式，便于集成和处理

## 📁 项目结构

```
绿盟漏扫查询漏洞/
├── README.md                 # 项目说明文档
├── API_Documentation.md      # API接口详细文档
├── requirements.txt          # Python依赖包列表
├── example.py               # 完整使用示例
├── login_manager.py         # 登录管理模块
├── task_manager.py          # 任务管理模块
├── vuln_manager.py          # 漏洞管理模块
├── vuln_api_server.py       # HTTP API服务器主程序
└── api_client_example.py    # API客户端使用示例
```

## 🛠️ 环境要求

- **Python**: 3.6 或更高版本
- **操作系统**: Windows/Linux/macOS
- **网络**: 能够访问绿盟扫描系统的网络环境

### 依赖包

- `requests` - HTTP请求处理
- `ddddocr` - 验证码自动识别
- `flask` - Web框架
- `flask-cors` - 跨域资源共享支持

## 🚀 快速安装

### 1. 克隆项目

```bash
git clone <repository-url>
cd 绿盟漏扫查询漏洞
```

### 2. 安装依赖

```bash
# 使用pip安装
pip install -r requirements.txt

# 或手动安装各个包
pip install requests ddddocr flask flask-cors
```

## 📖 使用指南

### 方式一：直接使用Python模块

```python
from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

# 1. 自动登录绿盟系统
login_manager = GreenLeagueLogin(host="************")
success, login_result = login_manager.auto_login()

if success:
    # 2. 初始化管理器
    session = login_manager.get_session()
    base_url = login_manager.get_base_url()

    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)

    # 3. 获取已完成的任务
    success, completed_tasks = task_manager.get_completed_tasks()

    # 4. 分析原理扫描漏洞
    if success and completed_tasks:
        task_ids = [task['task_id'] for task in completed_tasks]
        analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
            task_ids, focus_on_principle=True
        )

        # 5. 查看结果
        print(f"发现 {len(analysis_result['all_vulns'])} 个原理扫描漏洞")
```

### 方式二：运行完整示例

```bash
# 运行完整的使用示例
python example.py
```

### 方式三：启动HTTP API服务器（推荐）

```bash
# 启动API服务器
python vuln_api_server.py
```

服务器启动后：
- **端口**: 45678
- **API地址**: `http://localhost:45678`
- **自动登录**: 启动时自动登录绿盟系统
- **会话管理**: 自动维护登录状态

### 测试API接口

```bash
# 方式1: 使用客户端示例
python api_client_example.py

# 方式2: 使用curl命令
curl http://localhost:45678/
curl http://localhost:45678/principle-vulns

# 方式3: 使用浏览器
# 访问 http://localhost:45678
```

## 🔧 核心模块

### 🔐 LoginManager (登录管理)

自动化登录绿盟系统的核心模块：

**功能特性：**
- ✅ 验证码自动识别（基于ddddocr）
- ✅ 自动用户认证和会话管理
- ✅ SSL证书处理（支持自签名证书）
- ✅ 登录状态检测和维护

**核心方法：**
```python
auto_login()        # 一键自动登录
get_session()       # 获取认证会话对象
get_base_url()      # 获取系统基础URL
```

### 📋 TaskManager (任务管理)

扫描任务的查询和管理模块：

**功能特性：**
- ✅ 任务列表分页查询
- ✅ 任务状态智能过滤
- ✅ 任务统计和分析
- ✅ 已完成任务批量获取

**核心方法：**
```python
get_completed_tasks()      # 获取所有已完成任务
get_task_statistics()      # 获取任务统计信息
search_tasks_by_name()     # 按名称搜索任务
display_task_summary()     # 显示任务摘要
```

### 🛡️ VulnManager (漏洞管理)

漏洞信息查询和分析的专业模块：

**功能特性：**
- ✅ 漏洞分布统计查询
- ✅ 原理扫描漏洞智能过滤
- ✅ 多任务漏洞批量分析
- ✅ 漏洞等级和类型统计

**核心方法：**
```python
get_vuln_distribution()           # 获取漏洞分布统计
filter_principle_scan_vulns()     # 过滤原理扫描漏洞
analyze_multiple_tasks_vulns()    # 批量分析多任务漏洞
display_vuln_statistics()         # 显示详细漏洞统计
```

### 🌐 HTTP API服务器

提供标准RESTful API接口：

**API接口：**
- `GET /` - 服务器状态和系统信息
- `GET /principle-vulns` - 获取所有原理扫描漏洞

**服务特性：**
- ✅ 自动登录和会话管理
- ✅ 登录状态过期自动重新登录
- ✅ CORS跨域支持
- ✅ JSON标准响应格式
- ✅ 详细错误处理和日志

## 📚 API使用示例

### Python客户端示例

```python
import requests
import json

# API服务器地址
API_BASE = 'http://localhost:45678'

# 1. 检查服务器状态
def check_server_status():
    try:
        response = requests.get(f'{API_BASE}/')
        data = response.json()

        print(f"服务器状态: {data['status']}")
        print(f"登录时长: {data.get('login_duration', 'N/A')}")
        print(f"目标主机: {data['target_host']}")

        return data['status'] == '已登录'
    except Exception as e:
        print(f"服务器连接失败: {e}")
        return False

# 2. 获取原理扫描漏洞
def get_principle_vulns():
    try:
        response = requests.get(f'{API_BASE}/principle-vulns')
        data = response.json()

        if data['success']:
            vulns = data['data']['vulns']
            total_count = data['data']['total_count']
            task_count = data['data']['task_count']

            print(f"✓ 成功获取 {total_count} 个原理扫描漏洞")
            print(f"✓ 涉及 {task_count} 个已完成任务")

            # 显示漏洞统计
            stats = data['data']['statistics']
            print("\n漏洞等级分布:")
            for level, count in stats.get('severity_distribution', {}).items():
                print(f"  {level}: {count} 个")

            # 显示前5个漏洞
            print(f"\n前5个漏洞:")
            for vuln in vulns[:5]:
                print(f"  - {vuln.get('name', 'N/A')} ({vuln.get('severity', 'N/A')})")

        else:
            print(f"✗ 获取失败: {data['message']}")

    except Exception as e:
        print(f"API调用失败: {e}")

# 主程序
if __name__ == "__main__":
    if check_server_status():
        get_principle_vulns()
    else:
        print("请先启动API服务器: python vuln_api_server.py")
```

### curl命令示例

```bash
# 检查服务器状态
curl -s http://localhost:45678/ | python -m json.tool

# 获取原理扫描漏洞（格式化输出）
curl -s http://localhost:45678/principle-vulns | python -m json.tool

# 获取漏洞数据并保存到文件
curl -s http://localhost:45678/principle-vulns -o vulns_data.json

# 提取漏洞总数
curl -s http://localhost:45678/principle-vulns | python -c "
import json, sys
data = json.load(sys.stdin)
print(f'漏洞总数: {data[\"data\"][\"total_count\"]}' if data['success'] else '获取失败')
"
```

## ⚙️ 配置说明

### 默认配置

```python
# vuln_api_server.py 中的默认配置
DEFAULT_CONFIG = {
    "host": "************",        # 绿盟服务器地址
    "port": 45678,                 # API服务器端口
    "protocol": "HTTPS",           # 通信协议
    "ssl_verify": False,           # SSL证书验证（适用于自签名证书）
    "login_timeout": 600,          # 登录超时时间（秒）
    "debug": False                 # 调试模式
}
```

### 自定义配置

```python
# 方式1: 修改代码中的默认值
# 编辑 vuln_api_server.py 的 main() 函数

# 方式2: 创建自定义启动脚本
from vuln_api_server import VulnAPIServer

# 使用自定义配置
server = VulnAPIServer(
    host="your-server-ip",    # 自定义服务器地址
    server_port=8080          # 自定义端口
)
server.run(debug=True)        # 启用调试模式
```

## 💡 完整工作流程示例

```python
#!/usr/bin/env python3
"""
完整的绿盟漏洞查询工作流程示例
"""

def complete_workflow():
    """完整的漏洞查询和分析流程"""

    print("🚀 开始绿盟漏洞查询流程...")

    # 1. 初始化登录管理器
    print("📡 正在连接绿盟系统...")
    login_manager = GreenLeagueLogin(host="************")

    # 2. 自动登录
    print("🔐 正在自动登录...")
    success, login_result = login_manager.auto_login()

    if not success:
        print("❌ 登录失败，请检查网络连接和服务器状态")
        return False

    print("✅ 登录成功！")

    # 3. 初始化管理器
    session = login_manager.get_session()
    base_url = login_manager.get_base_url()
    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)

    # 4. 获取任务统计
    print("📊 正在获取任务统计...")
    task_manager.get_task_statistics()

    # 5. 获取已完成任务
    print("📋 正在获取已完成任务...")
    success, completed_tasks = task_manager.get_completed_tasks()

    if not success or not completed_tasks:
        print("⚠️  没有找到已完成的任务")
        return False

    print(f"✅ 找到 {len(completed_tasks)} 个已完成任务")

    # 6. 分析原理扫描漏洞
    print("🛡️  正在分析原理扫描漏洞...")
    task_ids = [task['task_id'] for task in completed_tasks]
    analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
        task_ids, focus_on_principle=True
    )

    # 7. 显示分析结果
    all_vulns = analysis_result['all_vulns']
    if all_vulns:
        print(f"🎯 发现 {len(all_vulns)} 个原理扫描漏洞")

        # 显示前10个漏洞详情
        vuln_manager.display_vuln_details(all_vulns[:10])

        # 显示统计信息
        vuln_manager.display_vuln_statistics(all_vulns)

        print("✅ 漏洞分析完成！")
        return True
    else:
        print("ℹ️  没有发现原理扫描漏洞")
        return False

if __name__ == "__main__":
    complete_workflow()
```

## ⚠️ 重要注意事项

### 安全配置
- **SSL证书**: 系统默认禁用SSL证书验证，适用于自签名证书环境
- **网络安全**: API服务器监听在 `0.0.0.0`，请确保防火墙配置正确
- **访问控制**: 建议在生产环境中添加认证和授权机制

### 技术要点
- **验证码识别**: 使用ddddocr库自动识别验证码，准确率>95%
- **会话管理**: 自动维护登录会话，无需手动处理Cookie
- **错误处理**: 完整的异常处理和自动重试机制
- **日志记录**: 详细的操作日志，便于问题排查

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 🚫 登录失败
```bash
# 问题现象
ERROR: 自动登录绿盟系统失败

# 解决步骤
1. 检查服务器地址: ping ************
2. 确认网络连接: telnet ************ 443
3. 验证防火墙设置
4. 检查系统时间是否正确
```

#### 2. 🔍 验证码识别失败
```bash
# 问题现象
验证码识别失败，登录中断

# 解决步骤
1. 重新安装ddddocr: pip uninstall ddddocr && pip install ddddocr
2. 检查Python版本: python --version (需要3.6+)
3. 清理缓存: pip cache purge
4. 手动测试验证码识别功能
```

#### 3. 🌐 API调用失败
```bash
# 问题现象
curl: (7) Failed to connect to localhost port 45678

# 解决步骤
1. 检查服务是否启动: ps aux | grep vuln_api_server
2. 确认端口未被占用: netstat -tlnp | grep 45678
3. 检查防火墙规则: ufw status
4. 查看服务器日志输出
```

#### 4. 📊 数据获取异常
```bash
# 问题现象
{"success": false, "message": "获取原理扫描漏洞异常"}

# 解决步骤
1. 检查登录状态: curl http://localhost:45678/
2. 验证会话有效性
3. 重启API服务器
4. 检查绿盟系统状态
```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 启动调试模式
server = VulnAPIServer(host="************", server_port=45678)
server.run(debug=True)
```

## 📖 相关文档

- **[API_Documentation.md](API_Documentation.md)** - 详细的API接口文档和curl测试命令
- **[requirements.txt](requirements.txt)** - Python依赖包列表
- **[example.py](example.py)** - 完整的使用示例代码

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. **Fork** 本项目
2. **创建** 特性分支: `git checkout -b feature/AmazingFeature`
3. **提交** 更改: `git commit -m 'Add some AmazingFeature'`
4. **推送** 分支: `git push origin feature/AmazingFeature`
5. **提交** Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## ⚡ 快速体验

```bash
# 一键启动体验
git clone <repository-url>
cd 绿盟漏扫查询漏洞
pip install -r requirements.txt
python vuln_api_server.py

# 测试API
curl http://localhost:45678/
curl http://localhost:45678/principle-vulns
```

---

## 📞 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查 [Issues](../../issues) 中是否有类似问题
3. 提交新的 Issue 并提供详细的错误信息和日志

**⚠️ 免责声明**: 本工具仅用于授权的安全测试和漏洞管理，请勿用于非法用途。使用者需遵守相关法律法规和企业安全政策。
